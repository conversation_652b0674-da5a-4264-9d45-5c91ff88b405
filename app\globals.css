@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(45, 55, 72, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(45, 55, 72, 0.6);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* RTL Support */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

:where([class^="ri-"])::before { content: "\f3c2"; }

body {
  direction: rtl;
  text-align: right;
  font-family: 'Tajawal', 'Poppins', sans-serif;
}

.ltr {
  direction: ltr;
  text-align: left;
}

.rtl {
  direction: rtl;
  text-align: right;
}

/* تحسينات إضافية للتصميم */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* تأثيرات الأزرار */
.btn-primary {
  background: linear-gradient(135deg, #1B1B3A 0%, #4A4A7D 100%);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4A4A7D 0%, #1B1B3A 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(27, 27, 58, 0.3);
}

/* تحسين الانتقالات */
* {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* تحسين التمرير */
html {
  scroll-behavior: smooth;
}

/* تحسين الـ sticky header لتجنب تحذيرات React */
header[class*="sticky"] {
  position: sticky !important;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* أنيميشن الرسالة المنبثقة */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

/* أنيميشن جديد للفئات */
@keyframes categorySlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes categoryPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-category-slide-in {
  animation: categorySlideIn 0.8s ease-out forwards;
}

.animate-category-pulse {
  animation: categoryPulse 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-icon-bounce {
  animation: iconBounce 2s infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* تأثيرات الفئات المحسنة */
.category-card {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.category-card:hover::before {
  left: 100%;
}

.category-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.category-image {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-card:hover .category-image {
  transform: scale(1.15) rotate(2deg);
}

.category-overlay {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.5) 100%);
  transition: all 0.3s ease;
}

.category-card:hover .category-overlay {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
}

.category-content {
  position: relative;
  z-index: 20;
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 70%, transparent 100%);
  backdrop-filter: blur(1px);
}

.category-title {
  transition: all 0.3s ease;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.8);
  color: #ffffff !important;
  font-weight: 800;
}

.category-card:hover .category-title {
  transform: translateY(-5px);
  text-shadow: 3px 3px 12px rgba(0,0,0,0.9);
  color: #ffffff !important;
}

.category-description {
  transition: all 0.3s ease;
  opacity: 0.95;
  text-shadow: 1px 1px 4px rgba(0,0,0,0.8);
  color: #f8f9fa !important;
}

.category-card:hover .category-description {
  opacity: 1;
  transform: translateY(-3px);
  text-shadow: 2px 2px 6px rgba(0,0,0,0.9);
  color: #ffffff !important;
}

.category-footer {
  background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(248,250,252,0.98) 100%);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border-top: 1px solid rgba(255,255,255,0.2);
}

.category-card:hover .category-footer {
  background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,250,252,1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
}

/* تحسين النصوص في القسم السفلي */
.category-footer h3 {
  color: #1f2937 !important;
  font-weight: 700;
  transition: all 0.3s ease;
}

.category-card:hover .category-footer h3 {
  color: #111827 !important;
  transform: translateY(-1px);
}

.category-footer p {
  color: #6b7280 !important;
  transition: all 0.3s ease;
}

.category-card:hover .category-footer p {
  color: #4b5563 !important;
}

.category-arrow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-card:hover .category-arrow {
  transform: translateX(8px) scale(1.2);
  color: #1B1B3A;
}

/* تحسينات إضافية للفئات */
.category-card {
  will-change: transform;
}

.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }
.category-card:nth-child(5) { animation-delay: 0.5s; }
.category-card:nth-child(6) { animation-delay: 0.6s; }
.category-card:nth-child(7) { animation-delay: 0.7s; }
.category-card:nth-child(8) { animation-delay: 0.8s; }

/* تأثير الخلفية المتحركة */
@keyframes backgroundMove {
  0% { transform: translateX(-100px) translateY(-100px); }
  50% { transform: translateX(100px) translateY(100px); }
  100% { transform: translateX(-100px) translateY(-100px); }
}

.animate-background-move {
  animation: backgroundMove 20s ease-in-out infinite;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
  .category-card:hover {
    transform: translateY(-8px) scale(1.01);
  }

  .category-card:hover .category-image {
    transform: scale(1.1);
  }
}

/* تحسينات الناف بار */
.navbar-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.navbar-shadow {
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.navbar-progress {
  background: linear-gradient(90deg, #2D3748, #4A5568);
  animation: shimmer 2s infinite;
}

.mobile-menu-item {
  animation: slideInLeft 0.3s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.4s; }
.mobile-menu-item:nth-child(5) { animation-delay: 0.5s; }

.cart-bounce {
  animation: pulse-glow 2s infinite;
}

/* تأثير الهوفر للروابط */
.nav-link-hover {
  position: relative;
  overflow: hidden;
}

.nav-link-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.nav-link-hover:hover::before {
  left: 100%;
}

/* تأثير التحميل للصور */
.category-image:not([src]) {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.category-image[src] {
  animation: none;
  background: none;
}

/* تحسين الأداء */
.category-card * {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* تأثيرات الهاتف المحمول */
@media (hover: none) and (pointer: coarse) {
  .category-card:active {
    transform: scale(0.98);
  }

  .category-card:active .category-image {
    transform: scale(1.05);
  }
}
